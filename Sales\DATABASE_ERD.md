# Smart Track Database Entity Relationship Diagram

This document provides a comprehensive Entity Relationship Diagram (ERD) for the Smart Track Sales Force Automation system database. The diagram illustrates all database tables, their relationships, and key constraints.

## Database ERD

```plantuml
@startuml SmartTrackERD

!define PRIMARY_KEY(x) <b><color:#b8861b><&key></color> x</b>
!define FOREIGN_KEY(x) <color:#aaaaaa><&key></color> x
!define COLUMN(x) <color:#efefef><&media-record></color> x

entity "users" as users {
  PRIMARY_KEY(id) : INTEGER
  --
  COLUMN(username) : VARCHAR(50) UNIQUE NOT NULL
  COLUMN(email) : VARCHAR(100) UNIQUE NOT NULL
  COLUMN(hashed_password) : VARCHAR(255) NOT NULL
  COLUMN(is_active) : BOOLEAN DEFAULT TRUE
  COLUMN(is_verified) : BOOLEAN DEFAULT FALSE
  FOREIGN_KEY(role_id) : INTEGER NOT NULL
  COLUMN(created_at) : TIMES<PERSON>MP DEFAULT NOW()
  COLUMN(updated_at) : TIMES<PERSON><PERSON>
}

entity "roles" as roles {
  PRIMARY_KEY(id) : INTEGER
  --
  COLUMN(name) : VARCHAR(50) UNIQUE NOT NULL
  COLUMN(description) : TEXT
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

entity "permissions" as permissions {
  PRIMARY_KEY(id) : INTEGER
  --
  COLUMN(name) : VARCHAR(100) UNIQUE NOT NULL
  COLUMN(description) : TEXT
  COLUMN(resource) : VARCHAR(50)
  COLUMN(action) : VARCHAR(50)
}

entity "role_permissions" as role_permissions {
  PRIMARY_KEY(id) : INTEGER
  --
  FOREIGN_KEY(role_id) : INTEGER NOT NULL
  FOREIGN_KEY(permission_id) : INTEGER NOT NULL
}

entity "user_profiles" as user_profiles {
  PRIMARY_KEY(id) : INTEGER
  --
  FOREIGN_KEY(user_id) : INTEGER UNIQUE NOT NULL
  COLUMN(first_name) : VARCHAR(50)
  COLUMN(last_name) : VARCHAR(50)
  COLUMN(phone) : VARCHAR(20)
  COLUMN(employee_id) : VARCHAR(20) UNIQUE
  COLUMN(department) : VARCHAR(50)
  COLUMN(designation) : VARCHAR(50)
  COLUMN(joining_date) : DATE
  FOREIGN_KEY(territory_id) : INTEGER
  FOREIGN_KEY(branch_id) : INTEGER
  FOREIGN_KEY(manager_id) : INTEGER
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
  COLUMN(updated_at) : TIMESTAMP
}

entity "regions" as regions {
  PRIMARY_KEY(id) : INTEGER
  --
  COLUMN(name) : VARCHAR(100) NOT NULL
  COLUMN(code) : VARCHAR(10) UNIQUE
  COLUMN(description) : TEXT
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

entity "branches" as branches {
  PRIMARY_KEY(id) : INTEGER
  --
  COLUMN(name) : VARCHAR(100) NOT NULL
  COLUMN(code) : VARCHAR(10) UNIQUE
  COLUMN(address) : TEXT
  COLUMN(city) : VARCHAR(50)
  COLUMN(state) : VARCHAR(50)
  COLUMN(postal_code) : VARCHAR(10)
  FOREIGN_KEY(region_id) : INTEGER NOT NULL
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

entity "territories" as territories {
  PRIMARY_KEY(id) : INTEGER
  --
  COLUMN(name) : VARCHAR(100) NOT NULL
  COLUMN(code) : VARCHAR(10) UNIQUE
  COLUMN(description) : TEXT
  FOREIGN_KEY(branch_id) : INTEGER NOT NULL
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

entity "customers" as customers {
  PRIMARY_KEY(id) : INTEGER
  --
  COLUMN(code) : VARCHAR(20) UNIQUE NOT NULL
  COLUMN(name) : VARCHAR(200) NOT NULL
  COLUMN(address) : TEXT
  COLUMN(city) : VARCHAR(50)
  COLUMN(state) : VARCHAR(50)
  COLUMN(postal_code) : VARCHAR(10)
  COLUMN(phone) : VARCHAR(20)
  COLUMN(email) : VARCHAR(100)
  COLUMN(customer_type) : VARCHAR(20)
  COLUMN(credit_limit) : DECIMAL(15,2)
  COLUMN(latitude) : FLOAT
  COLUMN(longitude) : FLOAT
  FOREIGN_KEY(territory_id) : INTEGER
  COLUMN(is_active) : BOOLEAN DEFAULT TRUE
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
  COLUMN(updated_at) : TIMESTAMP
}

entity "customer_contacts" as customer_contacts {
  PRIMARY_KEY(id) : INTEGER
  --
  FOREIGN_KEY(customer_id) : INTEGER NOT NULL
  COLUMN(name) : VARCHAR(100) NOT NULL
  COLUMN(designation) : VARCHAR(50)
  COLUMN(phone) : VARCHAR(20)
  COLUMN(email) : VARCHAR(100)
  COLUMN(is_primary) : BOOLEAN DEFAULT FALSE
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

entity "visits" as visits {
  PRIMARY_KEY(id) : INTEGER
  --
  FOREIGN_KEY(user_id) : INTEGER NOT NULL
  FOREIGN_KEY(customer_id) : INTEGER NOT NULL
  COLUMN(planned_date) : TIMESTAMP NOT NULL
  COLUMN(purpose) : TEXT
  COLUMN(status) : VARCHAR(20) DEFAULT 'planned'
  COLUMN(actual_start_time) : TIMESTAMP
  COLUMN(actual_end_time) : TIMESTAMP
  COLUMN(checkin_latitude) : FLOAT
  COLUMN(checkin_longitude) : FLOAT
  COLUMN(checkout_latitude) : FLOAT
  COLUMN(checkout_longitude) : FLOAT
  COLUMN(outcome) : TEXT
  COLUMN(next_action) : TEXT
  COLUMN(notes) : TEXT
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
  COLUMN(updated_at) : TIMESTAMP
}

entity "visit_photos" as visit_photos {
  PRIMARY_KEY(id) : INTEGER
  --
  FOREIGN_KEY(visit_id) : INTEGER NOT NULL
  COLUMN(photo_url) : VARCHAR(500) NOT NULL
  COLUMN(photo_type) : VARCHAR(20) DEFAULT 'general'
  COLUMN(caption) : TEXT
  COLUMN(latitude) : FLOAT
  COLUMN(longitude) : FLOAT
  COLUMN(file_size) : INTEGER
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

entity "visit_notes" as visit_notes {
  PRIMARY_KEY(id) : INTEGER
  --
  FOREIGN_KEY(visit_id) : INTEGER NOT NULL
  COLUMN(note_type) : VARCHAR(20) DEFAULT 'general'
  COLUMN(title) : VARCHAR(200)
  COLUMN(content) : TEXT NOT NULL
  COLUMN(priority) : VARCHAR(10) DEFAULT 'medium'
  COLUMN(is_resolved) : BOOLEAN DEFAULT FALSE
  COLUMN(resolved_at) : TIMESTAMP
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

entity "visit_routes" as visit_routes {
  PRIMARY_KEY(id) : INTEGER
  --
  FOREIGN_KEY(user_id) : INTEGER NOT NULL
  COLUMN(date) : DATE NOT NULL
  COLUMN(optimized_route) : JSON
  COLUMN(total_distance) : FLOAT
  COLUMN(estimated_duration) : INTEGER
  COLUMN(status) : VARCHAR(20) DEFAULT 'planned'
  COLUMN(created_at) : TIMESTAMP DEFAULT NOW()
}

' Relationships

' User Management Relationships
users ||--|| user_profiles : "1:1"
users }o--|| roles : "N:1"
roles ||--o{ role_permissions : "1:N"
permissions ||--o{ role_permissions : "1:N"
users ||--o{ user_profiles : "1:N (manager)"

' Organizational Structure Relationships
regions ||--o{ branches : "1:N"
branches ||--o{ territories : "1:N"
territories ||--o{ user_profiles : "1:N"
territories ||--o{ customers : "1:N"

' Customer Management Relationships
customers ||--o{ customer_contacts : "1:N"

' Visit Management Relationships
users ||--o{ visits : "1:N"
customers ||--o{ visits : "1:N"
visits ||--o{ visit_photos : "1:N"
visits ||--o{ visit_notes : "1:N"
users ||--o{ visit_routes : "1:N"

@enduml
```

## Database Structure Overview

### Core Entity Groups

#### 1. **User Management & Authentication**
- **users**: Core user accounts with authentication credentials
- **roles**: Role-based access control (admin, sales_manager, sales_rep)
- **permissions**: Granular permissions for different system actions
- **role_permissions**: Many-to-many relationship between roles and permissions
- **user_profiles**: Extended user information including organizational hierarchy

#### 2. **Organizational Structure**
- **regions**: Top-level geographic divisions
- **branches**: Regional offices or branches
- **territories**: Sales territories within branches
- Hierarchical structure: Region → Branch → Territory → Users/Customers

#### 3. **Customer Management**
- **customers**: Customer master data with GPS coordinates for location services
- **customer_contacts**: Multiple contact persons per customer
- GPS coordinates enable location-based features and visit validation

#### 4. **Visit Management System**
- **visits**: Core visit records linking users to customers with GPS tracking
- **visit_photos**: Photo documentation with categorization and GPS metadata
- **visit_notes**: Structured notes with priority levels and resolution tracking
- **visit_routes**: Route optimization and planning for efficient field visits

### Key Relationships

#### **Hierarchical Relationships**
- **User Hierarchy**: `user_profiles.manager_id` → `users.id` (self-referential)
- **Organizational**: `regions` → `branches` → `territories` → `users`/`customers`
- **Permission Model**: `roles` ↔ `permissions` (many-to-many via `role_permissions`)

#### **Operational Relationships**
- **Visit Assignment**: `users` → `visits` ← `customers` (many-to-many through visits)
- **Content Management**: `visits` → `visit_photos`/`visit_notes` (one-to-many)
- **Location Services**: GPS coordinates in `customers` and `visits` enable proximity validation

#### **Data Integrity Constraints**
- **Territory Alignment**: Users and customers in same territory for visit assignment
- **GPS Validation**: Visit check-in coordinates validated against customer location
- **Role-Based Access**: User permissions determined by role assignments

### Business Logic Implementation

#### **Visit Workflow**
1. **Planning**: Create visit linking user to customer in same territory
2. **Execution**: GPS check-in validates location within 500m of customer
3. **Documentation**: Photos and notes captured with GPS metadata
4. **Completion**: GPS check-out with outcome recording

#### **Location Services**
- **Proximity Validation**: Haversine formula calculates distance between coordinates
- **Route Optimization**: `visit_routes` stores optimized paths for multiple visits
- **Geographic Analytics**: Territory-based performance analysis

#### **Security Model**
- **Authentication**: JWT tokens with bcrypt password hashing
- **Authorization**: Role-based permissions for API endpoints
- **Data Isolation**: Users can only access data within their territory/branch

This ERD represents a production-ready database schema supporting comprehensive field sales automation with GPS tracking, photo documentation, and organizational hierarchy management.